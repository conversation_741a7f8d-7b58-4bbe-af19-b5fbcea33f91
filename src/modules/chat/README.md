# 聊天会话管理 API

## 概述

本模块提供聊天会话管理相关的 API 接口，包括创建会话、获取历史会话列表、获取历史消息详情和反馈功能。

## 基础信息

- **基础路径**: `/web-assistant/v1/chat`
- **服务器地址**: `http://localhost:3000` (本地开发环境)
- **API 文档**: `http://localhost:3000/api/docs`

## 接口列表

### 1. 创建会话

**接口地址**: `POST /web-assistant/v1/chat/create_session`

**描述**: 初始化创建一个新的聊天会话

**请求参数**:
```json
{
  "appid": "aorta",           // 系统id，标识平台
  "userid": "user123",        // 当前系统登录用户id
  "appKey": "your-app-key"    // hiagent接口涉及，鉴权字段（可选）
}
```

**响应示例**:
```json
{
  "code": "0",
  "msg": "操作成功",
  "resultData": {
    "conversationId": "conv_123456789"
  }
}
```

### 2. 获取历史会话列表

**接口地址**: `GET /web-assistant/v1/chat/get_history_sessions`

**描述**: 获取用户的历史聊天会话列表

**请求参数**:
```
appid=aorta&userid=user123&appKey=your-app-key
```

**响应示例**:
```json
{
  "code": "0",
  "msg": "操作成功",
  "resultData": {
    "list": [
      {
        "conversationId": "conv_123456789",
        "question": "你好，请问如何使用这个系统？",
        "createTime": "2024-01-15 10:30:00",
        "updateTime": "2024-01-15 11:45:00"
      }
    ]
  }
}
```

### 3. 获取历史消息详情

**接口地址**: `GET /web-assistant/v1/chat/get_history_messages`

**描述**: 分页获取指定会话的历史消息详情

**请求参数**:
```
appid=aorta&userid=user123&pageNum=1&pageSize=6&conversationId=conv_123456789&appKey=your-app-key
```

**响应示例**:
```json
{
  "code": "0",
  "msg": "操作成功",
  "resultData": {
    "list": [
      {
        "messageId": "msg_123456789",
        "content": "这是一条消息内容",
        "type": "text",
        "sender": "user",
        "createTime": "2024-01-15 10:30:00"
      }
    ],
    "noMore": false
  }
}
```

### 4. 点赞/点踩接口

**接口地址**: `POST /web-assistant/v1/chat/feedback`

**描述**: 对AI回答进行点赞或点踩反馈

**请求参数**:
```json
{
  "appid": "aorta",
  "userid": "user123",
  "conversationId": "conv_123456789",
  "messageId": "msg_123456789",
  "score": "good",              // "good" 或 "bad"
  "type": "submit",             // "submit" 或 "reset"
  "reason": {                   // 点踩时的原因（可选）
    "selectedIds": ["reason1", "reason2"],
    "inputReason": "回答不够准确"
  },
  "appKey": "your-app-key"      // 可选
}
```

**响应示例**:
```json
{
  "code": "0",
  "msg": "操作成功"
}
```

## 错误码说明

- `code: "0"` - 操作成功
- `code: 非"0"` - 操作失败，具体错误信息见 `msg` 字段

## 使用示例 (Postman)

### 创建会话示例

```
POST http://localhost:3000/web-assistant/v1/chat/create_session
Content-Type: application/json

{
  "appid": "aorta",
  "userid": "user123"
}
```

### 获取历史会话列表示例

```
GET http://localhost:3000/web-assistant/v1/chat/get_history_sessions?appid=aorta&userid=user123
```

## 注意事项

1. 所有接口都支持通过 Headers 传递额外的认证信息
2. 分页参数 `pageSize` 默认值为 6
3. `appKey` 字段在所有接口中都是可选的，用于 hiagent 接口鉴权
4. 点踩时可以提供 `reason` 字段说明原因
5. 反馈接口的 `type` 字段：首次点击传 "submit"，已点击过再次点击传 "reset"
